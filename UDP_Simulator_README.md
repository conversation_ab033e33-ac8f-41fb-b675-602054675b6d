# UDP态势数据模拟器

## 概述

本模拟器根据系统中的ControlMessageDecodeHandler解析逻辑，模拟生成标准的无人机和无人艇态势数据，通过UDP协议循环发送到指定的目标地址。

## 功能特性

### 1. 数据类型支持
- **无人机态势数据** (包类型: 0x04)
- **无人艇态势数据** (包类型: 0x03)

### 2. 数据结构完整性
- 完全按照系统协议规范构建数据包
- 包含48字节标准报文头
- 支持多目标数据发送
- 实时位置和状态模拟

### 3. 模拟特性
- **无人机**: 圆形飞行轨迹，高度变化，姿态模拟
- **无人艇**: 直线航行轨迹，海浪摆动，舵角变化
- **实时数据**: 位置、速度、姿态等参数实时更新

## 数据包结构

### 报文头 (48字节)
```
包序号 (2字节) + 信源 (2字节) + 信宿 (2字节) + 包类型 (1字节) + 
包长度 (2字节) + 分段数 (1字节) + 分段号 (1字节) + 预留 (37字节)
```

### 无人机态势数据
```
接口类型 (1字节) + 目标数量 (1字节) + 
[目标序号 (1字节) + 目标类型 (2字节) + 目标代号 (8字节) + 目标名称 (20字节) +
 相对高度 (4字节) + GPS高度 (4字节) + 空速 (4字节) + 地速 (4字节) +
 经度 (4字节) + 纬度 (4字节) + 俯仰 (4字节) + 滚转 (4字节) + 偏航 (4字节) +
 链路状态 (1字节)] * N +
执行任务数量 (2字节)
```

### 无人艇态势数据
```
接口类型 (1字节) + 目标数量 (1字节) +
[目标序号 (1字节) + 目标类型 (2字节) + 目标代号 (8字节) + 目标名称 (20字节) +
 经度 (4字节) + 纬度 (4字节) + 横摇 (4字节) + 纵摇 (4字节) + 艏相角 (4字节) +
 速度方向 (4字节) + 速度大小 (4字节) + 时间 (8字节) + 舵角 (4字节) +
 控制权 (1字节) + 控制模式 (1字节) + 惯导模式 (4字节) + 链路状态 (1字节)] * N +
执行任务数量 (2字节)
```

## 字段编码规则

### 位置字段
- **经纬度**: 实际值 × 10,000,000 (PositionField)
- **无人机高度/速度**: 实际值 × 100 (UavPositionField)
- **角度**: 实际值 × 10 (AngleField)
- **舵角**: 实际值 × 1000 ÷ 30 (RudderAngleField)

### 状态字段
- **链路状态**: 位字段 (bit0=电台链路, bit1=测控链路)
- **控制权**: 1=有控制权, 0=无控制权
- **控制模式**: 1=手动, 2=自动

## 使用方法

### 1. 编译运行
```bash
# 编译
javac -cp ".:lib/*" src/main/java/com/gy/show/simulator/*.java

# 运行（使用默认参数）
java -cp ".:lib/*" com.gy.show.simulator.SimulatorApplication

# 运行（指定参数）
java -cp ".:lib/*" com.gy.show.simulator.SimulatorApplication <host> <port> <uav_interval> <ship_interval>
```

### 2. 参数说明
- `host`: 目标主机地址 (默认: localhost)
- `uav_port`: 无人机数据端口 (默认: 8080)
- `ship_port`: 无人艇数据端口 (默认: 8081)
- `uav_interval`: 无人机数据发送间隔，秒 (默认: 2)
- `ship_interval`: 无人艇数据发送间隔，秒 (默认: 3)

### 3. 运行示例
```bash
# 发送到本地，无人机8080端口，无人艇8081端口，间隔2秒和3秒
java com.gy.show.simulator.SimulatorApplication localhost 8080 8081 2 3

# 发送到远程服务器，自定义端口
java com.gy.show.simulator.SimulatorApplication ************* 9999 9998 1 2

# 简化版测试器
java SimpleUdpSimulator send
```

### 4. 交互命令
程序运行后支持以下命令：
- `q`, `quit`, `exit`: 退出程序
- `s`, `status`: 显示状态
- `h`, `help`: 显示帮助

## 模拟数据说明

### 无人机数据 (2个目标)
- **UAV-Alpha**: 北京地区圆形飞行，高度1000m±100m
- **UAV-Beta**: 北京地区圆形飞行，高度1200m±100m，不同相位

### 无人艇数据 (2个目标)
- **SHIP-Alpha**: 上海地区直线航行，速度15节±2节
- **SHIP-Beta**: 上海地区直线航行，速度12节±2节，不同航线

### 动态特性
- **位置更新**: 基于时间的连续轨迹
- **姿态变化**: 模拟真实的飞行/航行姿态
- **状态变化**: 链路状态、控制模式等随机变化

## 日志输出

### INFO级别
- 启动/停止信息
- 数据发送统计
- 配置参数显示

### DEBUG级别
- 详细的目标位置、速度、姿态信息
- 数据包构建详情

## 注意事项

1. **端口配置**: 确保目标端口与系统监听端口一致
2. **网络连通性**: 确保网络连接正常
3. **数据格式**: 严格按照系统协议规范生成数据
4. **性能考虑**: 高频发送时注意系统性能影响
5. **字节序**: 数据包使用大端字节序（Big Endian）

## 扩展功能

### 添加新的目标类型
1. 在`SituationDataGenerator`中添加新的数据模型
2. 实现对应的数据包生成方法
3. 在`UdpDataSimulator`中添加发送逻辑

### 自定义轨迹
1. 修改`updateUavPositions`或`updateShipPositions`方法
2. 实现自定义的轨迹算法
3. 支持从文件加载轨迹数据

### 数据记录
1. 添加数据包记录功能
2. 支持回放历史数据
3. 数据包分析和验证

## 故障排除

### 常见问题
1. **连接被拒绝**: 检查目标主机和端口
2. **数据格式错误**: 检查字段编码和字节序
3. **发送频率过高**: 调整发送间隔参数
4. **内存占用过高**: 检查数据包大小和发送频率

### 调试方法
1. 启用DEBUG日志级别
2. 使用网络抓包工具验证数据包
3. 检查系统日志确认数据接收情况
