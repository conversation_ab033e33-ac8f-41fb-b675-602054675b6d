# TaskMonitor 任务进度实时推送实现

## 概述

根据 `TaskMonitor.java` 文件中的注释要求，实现了一个完整的任务进度实时推送到前端的功能。该实现包括：

1. 获取当前时间在任务开始时间和结束时间之间的任务集合
2. 根据当前时间计算任务执行的百分比
3. 将任务集合组装后通过WebSocket推送到前端

## 实现的功能

### 1. 核心功能

- **实时任务监控**: 每5秒自动查询当前正在执行的任务
- **进度计算**: 基于任务开始时间、结束时间和当前时间精确计算任务执行进度
- **WebSocket推送**: 通过 `FULL_VIEW_TASK_PROGRESS` 类型实时推送任务进度数据到前端
- **丰富的数据结构**: 提供完整的任务信息，包括进度百分比、剩余时间、已执行时间等

### 2. 新增/修改的文件

#### 新增文件：
- `src/main/java/com/gy/show/entity/dto/TaskProgressDTO.java` - 任务进度数据传输对象
- `src/main/java/com/gy/show/controller/TaskProgressController.java` - 任务进度API控制器
- `TaskMonitor_Implementation_README.md` - 实现说明文档

#### 修改文件：
- `src/main/java/com/gy/show/runnner/TaskMonitor.java` - 主要实现逻辑
- `src/main/java/com/gy/show/service/RequirementTaskService.java` - 添加查询接口
- `src/main/java/com/gy/show/service/impl/RequirementTaskServiceImpl.java` - 实现查询逻辑
- `src/main/java/com/gy/show/ws/FullViewTsServer.java` - 添加任务进度推送方法
- `src/main/java/com/gy/show/enums/TaskTypeEnum.java` - 添加根据code获取枚举的方法

## 技术实现细节

### 1. 任务查询逻辑

```java
// 查询条件：
// 1. 开始时间 <= 当前时间
// 2. 结束时间 >= 当前时间  
// 3. 任务状态 = 1 (执行中)
List<RequirementTask> tasks = list(Wrappers.<RequirementTask>lambdaQuery()
    .le(RequirementTask::getStartTime, now)
    .ge(RequirementTask::getEndTime, now)
    .eq(RequirementTask::getStatus, 1));
```

### 2. 进度计算算法

```java
// 进度计算公式：
// progress = (当前时间 - 开始时间) / (结束时间 - 开始时间)
long totalDuration = Duration.between(startTime, endTime).getSeconds();
long elapsedDuration = Duration.between(startTime, currentTime).getSeconds();
double progress = (double) elapsedDuration / totalDuration;
```

### 3. WebSocket推送

- **推送类型**: `FULL_VIEW_TASK_PROGRESS` (code: 14)
- **推送频率**: 每5秒一次
- **数据格式**: `List<TaskProgressDTO>`

### 4. 数据结构

`TaskProgressDTO` 包含以下关键字段：
- `taskId`: 任务ID
- `taskName`: 任务名称
- `progress`: 进度 (0.0-1.0)
- `progressPercentage`: 进度百分比 (0-100)
- `remainingSeconds`: 剩余时间（秒）
- `elapsedSeconds`: 已执行时间（秒）
- `totalSeconds`: 总时长（秒）
- `statusName`: 状态名称
- `taskTypeName`: 任务类型名称

## 使用方式

### 1. 自动启动

系统启动后，`TaskMonitor` 会自动开始工作：
- 实现了 `ApplicationRunner` 接口，在应用启动时初始化
- 使用 `@Scheduled(fixedRate = 5000)` 每5秒执行一次任务进度推送

### 2. API接口

提供了REST API用于手动查询当前执行任务：

```
GET /api/task-progress/current
```

### 3. WebSocket连接

前端需要连接到WebSocket端点：
```
/ws/panoramic/situation/{clientId}
```

监听消息类型为 `14` (`FULL_VIEW_TASK_PROGRESS`) 的消息。

## 配置说明

### 1. 推送频率配置

可以通过修改 `@Scheduled(fixedRate = 5000)` 中的值来调整推送频率（毫秒）。

### 2. 日志级别

- `INFO`: 显示任务数量统计
- `DEBUG`: 显示详细的任务进度信息

## 错误处理

- 所有异常都会被捕获并记录日志，不会影响系统稳定性
- 当没有正在执行的任务时，会记录DEBUG级别日志
- 计算进度时处理了边界情况（任务未开始、已结束、零时长等）

## 扩展性

该实现具有良好的扩展性：
- 可以轻松添加更多的任务信息字段
- 支持不同的推送频率配置
- 可以根据需要添加过滤条件
- 支持多种任务状态的监控

## 注意事项

1. 确保数据库中的任务状态字段正确设置
2. WebSocket连接需要正确的clientId
3. 任务的开始时间和结束时间必须合理设置
4. 建议在生产环境中适当调整推送频率以平衡实时性和性能
