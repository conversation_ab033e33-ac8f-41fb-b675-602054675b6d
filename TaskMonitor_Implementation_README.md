# TaskMonitor 任务进度实时推送实现（优化版）

## 概述

根据 `TaskMonitor.java` 文件中的注释要求，实现了一个完整的任务进度实时推送到前端的功能，并根据需求进行了优化。该实现包括：

1. 获取当前时间在任务开始时间和结束时间之间的任务集合
2. 根据当前时间计算任务执行的百分比
3. 将任务集合组装后通过WebSocket推送到前端
4. **新增**: 自动任务状态管理功能

## 实现的功能

### 1. 核心功能

- **实时任务监控**: 每5秒自动查询当前正在执行的任务
- **智能状态管理**: 自动将待执行任务转为执行中，将已结束任务标记为完成
- **进度计算**: 基于任务开始时间、结束时间和当前时间精确计算任务执行进度
- **WebSocket推送**: 通过 `FULL_VIEW_TASK_PROGRESS` 类型实时推送任务进度数据到前端
- **丰富的数据结构**: 提供完整的任务信息，包括进度百分比、剩余时间、已执行时间等

### 2. 新增优化功能

- **自动状态转换**:
  - 待执行(0) → 执行中(1): 当前时间 >= 任务开始时间
  - 执行中(1) → 执行成功(2): 当前时间 > 任务结束时间
- **批量状态更新**: 高效的批量数据库更新操作
- **状态管理API**: 提供手动状态管理接口
- **高精度进度计算**:
  - 进度计算保留4位小数精度
  - 百分比显示保留2位小数，如23.43%
  - 使用BigDecimal确保计算精确性

### 2. 新增/修改的文件

#### 新增文件：
- `src/main/java/com/gy/show/entity/dto/TaskProgressDTO.java` - 任务进度数据传输对象
- `src/main/java/com/gy/show/controller/TaskProgressController.java` - 任务进度API控制器
- `src/main/java/com/gy/show/util/ProgressCalculator.java` - 高精度进度计算工具类
- `src/test/java/com/gy/show/util/ProgressCalculatorTest.java` - 进度计算测试类
- `TaskMonitor_Implementation_README.md` - 实现说明文档

#### 修改文件：
- `src/main/java/com/gy/show/runnner/TaskMonitor.java` - 主要实现逻辑，添加状态管理功能
- `src/main/java/com/gy/show/service/RequirementTaskService.java` - 添加查询和状态更新接口
- `src/main/java/com/gy/show/service/impl/RequirementTaskServiceImpl.java` - 实现查询和批量更新逻辑
- `src/main/java/com/gy/show/ws/FullViewTsServer.java` - 添加任务进度推送方法
- `src/main/java/com/gy/show/enums/TaskTypeEnum.java` - 添加根据code获取枚举的方法
- `src/main/java/com/gy/show/controller/TaskProgressController.java` - 扩展API接口

## 技术实现细节

### 1. 任务查询逻辑

```java
// 查询条件：
// 1. 开始时间 <= 当前时间
// 2. 结束时间 >= 当前时间
// 3. 任务状态 = 0 (待执行) 或 1 (执行中)
List<RequirementTask> tasks = list(Wrappers.<RequirementTask>lambdaQuery()
    .le(RequirementTask::getStartTime, now)
    .ge(RequirementTask::getEndTime, now)
    .in(RequirementTask::getStatus, Arrays.asList(0, 1)));
```

### 2. 进度计算算法（高精度）

```java
// 进度计算公式：
// progress = (当前时间 - 开始时间) / (结束时间 - 开始时间)
// 使用BigDecimal进行精确计算，保留4位小数
BigDecimal elapsed = new BigDecimal(elapsedDuration);
BigDecimal total = new BigDecimal(totalDuration);
BigDecimal progress = elapsed.divide(total, 4, RoundingMode.HALF_UP);

// 百分比显示保留2位小数，例如：23.43%
double percentage = ProgressCalculator.toPercentage(progress, 2);
```

### 3. 状态管理逻辑

```java
// 状态转换规则：
// 待执行 → 执行中: 当前时间 >= 开始时间
if (task.getStatus() == 0 && !now.isBefore(task.getStartTime())) {
    tasksToStart.add(task.getId());
}

// 执行中 → 执行成功: 当前时间 > 结束时间
if (task.getStatus() == 1 && now.isAfter(task.getEndTime())) {
    tasksToComplete.add(task.getId());
}
```

### 4. WebSocket推送

- **推送类型**: `FULL_VIEW_TASK_PROGRESS` (code: 14)
- **推送频率**: 每5秒一次
- **数据格式**: `List<TaskProgressDTO>`

### 4. 数据结构

`TaskProgressDTO` 包含以下关键字段：
- `taskId`: 任务ID
- `taskName`: 任务名称
- `progress`: 进度 (0.0-1.0)
- `progressPercentage`: 进度百分比 (0-100)，保留2位小数，如23.43
- `remainingSeconds`: 剩余时间（秒）
- `elapsedSeconds`: 已执行时间（秒）
- `totalSeconds`: 总时长（秒）
- `statusName`: 状态名称
- `taskTypeName`: 任务类型名称

## 使用方式

### 1. 自动启动

系统启动后，`TaskMonitor` 会自动开始工作：
- 实现了 `ApplicationRunner` 接口，在应用启动时初始化
- 使用 `@Scheduled(fixedRate = 5000)` 每5秒执行一次任务进度推送

### 2. API接口

提供了丰富的REST API接口：

```
GET /api/task-progress/current              # 获取当前正在执行的任务
GET /api/task-progress/status-update        # 获取需要状态更新的任务
POST /api/task-progress/update-status       # 手动更新任务状态
POST /api/task-progress/auto-update-status  # 手动触发自动状态更新
```

### 3. WebSocket连接

前端需要连接到WebSocket端点：
```
/ws/panoramic/situation/{clientId}
```

监听消息类型为 `14` (`FULL_VIEW_TASK_PROGRESS`) 的消息。

## 配置说明

### 1. 推送频率配置

可以通过修改 `@Scheduled(fixedRate = 5000)` 中的值来调整推送频率（毫秒）。

### 2. 日志级别

- `INFO`: 显示任务数量统计
- `DEBUG`: 显示详细的任务进度信息

## 错误处理

- 所有异常都会被捕获并记录日志，不会影响系统稳定性
- 当没有正在执行的任务时，会记录DEBUG级别日志
- 计算进度时处理了边界情况（任务未开始、已结束、零时长等）

## 扩展性

该实现具有良好的扩展性：
- 可以轻松添加更多的任务信息字段
- 支持不同的推送频率配置
- 可以根据需要添加过滤条件
- 支持多种任务状态的监控

## 优化后的执行流程

1. **状态检查与更新** (每5秒)
   - 查询所有待执行(0)和执行中(1)的任务
   - 自动将到时间的待执行任务改为执行中
   - 自动将已结束的执行中任务改为执行成功

2. **进度计算与推送**
   - 查询当前正在执行的任务
   - 计算每个任务的执行进度
   - 通过WebSocket推送到前端

## 注意事项

1. 确保数据库中的任务状态字段正确设置
2. WebSocket连接需要正确的clientId
3. 任务的开始时间和结束时间必须合理设置
4. 建议在生产环境中适当调整推送频率以平衡实时性和性能
5. **新增**: 状态自动管理功能会修改数据库，请确保有适当的备份策略
6. **新增**: 批量更新操作使用事务，确保数据一致性
