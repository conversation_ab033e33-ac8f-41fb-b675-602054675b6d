package com.gy.show.util;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ProgressCalculator 测试类
 */
public class ProgressCalculatorTest {

    @Test
    public void testCalculateProgress() {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 10, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2024, 1, 1, 12, 0, 0); // 2小时后
        
        // 测试任务未开始
        LocalDateTime beforeStart = LocalDateTime.of(2024, 1, 1, 9, 0, 0);
        double progress1 = ProgressCalculator.calculateProgress(startTime, endTime, beforeStart);
        assertEquals(0.0, progress1);
        
        // 测试任务已结束
        LocalDateTime afterEnd = LocalDateTime.of(2024, 1, 1, 13, 0, 0);
        double progress2 = ProgressCalculator.calculateProgress(startTime, endTime, afterEnd);
        assertEquals(1.0, progress2);
        
        // 测试任务进行中 - 50%
        LocalDateTime middle = LocalDateTime.of(2024, 1, 1, 11, 0, 0);
        double progress3 = ProgressCalculator.calculateProgress(startTime, endTime, middle);
        assertEquals(0.5, progress3);
        
        // 测试任务进行中 - 25%
        LocalDateTime quarter = LocalDateTime.of(2024, 1, 1, 10, 30, 0);
        double progress4 = ProgressCalculator.calculateProgress(startTime, endTime, quarter);
        assertEquals(0.25, progress4);
        
        // 测试精确计算 - 23.43%的情况
        // 2小时 = 7200秒，23.43% = 1686.96秒 ≈ 28分6.96秒
        LocalDateTime precise = LocalDateTime.of(2024, 1, 1, 10, 28, 7); // 28分7秒
        double progress5 = ProgressCalculator.calculateProgress(startTime, endTime, precise);
        // 28分7秒 = 1687秒，1687/7200 = 0.2343
        assertEquals(0.2343, progress5, 0.0001);
    }
    
    @Test
    public void testToPercentage() {
        // 测试默认2位小数
        double percentage1 = ProgressCalculator.toPercentage(0.2343);
        assertEquals(23.43, percentage1);
        
        // 测试指定小数位数
        double percentage2 = ProgressCalculator.toPercentage(0.23456789, 4);
        assertEquals(23.4568, percentage2);
        
        // 测试边界值
        double percentage3 = ProgressCalculator.toPercentage(0.0);
        assertEquals(0.0, percentage3);
        
        double percentage4 = ProgressCalculator.toPercentage(1.0);
        assertEquals(100.0, percentage4);
    }
    
    @Test
    public void testFormatPercentage() {
        // 测试默认格式化
        String formatted1 = ProgressCalculator.formatPercentage(0.2343);
        assertEquals("23.43%", formatted1);
        
        // 测试指定小数位数
        String formatted2 = ProgressCalculator.formatPercentage(0.23456789, 4);
        assertEquals("23.4568%", formatted2);
        
        // 测试边界值
        String formatted3 = ProgressCalculator.formatPercentage(0.0);
        assertEquals("0.00%", formatted3);
        
        String formatted4 = ProgressCalculator.formatPercentage(1.0);
        assertEquals("100.00%", formatted4);
    }
    
    @Test
    public void testZeroDuration() {
        // 测试零时长任务
        LocalDateTime sameTime = LocalDateTime.of(2024, 1, 1, 10, 0, 0);
        double progress = ProgressCalculator.calculateProgress(sameTime, sameTime, sameTime);
        assertEquals(1.0, progress);
    }
    
    @Test
    public void testPrecisionAccuracy() {
        // 测试精度准确性
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 10, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2024, 1, 1, 10, 0, 10); // 10秒后
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 1, 10, 0, 2, 343000000); // 2.343秒后
        
        double progress = ProgressCalculator.calculateProgress(startTime, endTime, currentTime);
        // 2.343 / 10 = 0.2343
        assertEquals(0.2343, progress);
        
        // 转换为百分比应该是23.43%
        double percentage = ProgressCalculator.toPercentage(progress);
        assertEquals(23.43, percentage);
    }
}
