package com.gy.show.runnner;

import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.service.RequirementTaskService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * TaskMonitor 测试类
 */
@ExtendWith(MockitoExtension.class)
public class TaskMonitorTest {

    @Mock
    private RequirementTaskService requirementTaskService;

    @InjectMocks
    private TaskMonitor taskMonitor;

    @Test
    public void testUpdateTaskStatuses() {
        // 准备测试数据
        LocalDateTime now = LocalDateTime.now();
        
        // 待执行任务（应该被启动）
        RequirementTaskDTO pendingTask = new RequirementTaskDTO();
        pendingTask.setId("task1");
        pendingTask.setStatus(0); // 待执行
        pendingTask.setStartTime(now.minusMinutes(5)); // 5分钟前开始
        pendingTask.setEndTime(now.plusMinutes(10)); // 10分钟后结束
        
        // 执行中任务（应该被完成）
        RequirementTaskDTO executingTask = new RequirementTaskDTO();
        executingTask.setId("task2");
        executingTask.setStatus(1); // 执行中
        executingTask.setStartTime(now.minusMinutes(15)); // 15分钟前开始
        executingTask.setEndTime(now.minusMinutes(5)); // 5分钟前结束
        
        // 正常执行中任务（不应该被修改）
        RequirementTaskDTO normalTask = new RequirementTaskDTO();
        normalTask.setId("task3");
        normalTask.setStatus(1); // 执行中
        normalTask.setStartTime(now.minusMinutes(5)); // 5分钟前开始
        normalTask.setEndTime(now.plusMinutes(5)); // 5分钟后结束

        List<RequirementTaskDTO> allTasks = Arrays.asList(pendingTask, executingTask, normalTask);

        // Mock 服务方法
        when(requirementTaskService.queryTasksForStatusUpdate()).thenReturn(allTasks);

        // 执行测试方法
        // 由于 updateTaskStatuses 是私有方法，我们通过 pushTaskProgress 来间接测试
        when(requirementTaskService.queryCurrentExecutingTasks()).thenReturn(Arrays.asList(normalTask));
        
        taskMonitor.pushTaskProgress();

        // 验证状态更新调用
        verify(requirementTaskService).batchUpdateTaskStatus(Arrays.asList("task1"), 1); // 启动任务
        verify(requirementTaskService).batchUpdateTaskStatus(Arrays.asList("task2"), 2); // 完成任务
        verify(requirementTaskService).queryCurrentExecutingTasks();
    }

    @Test
    public void testPushTaskProgressWithNoTasks() {
        // Mock 空任务列表
        when(requirementTaskService.queryTasksForStatusUpdate()).thenReturn(Arrays.asList());
        when(requirementTaskService.queryCurrentExecutingTasks()).thenReturn(Arrays.asList());

        // 执行测试
        taskMonitor.pushTaskProgress();

        // 验证只调用了查询方法，没有调用更新方法
        verify(requirementTaskService).queryTasksForStatusUpdate();
        verify(requirementTaskService).queryCurrentExecutingTasks();
        verify(requirementTaskService, never()).batchUpdateTaskStatus(any(), any());
    }
}
