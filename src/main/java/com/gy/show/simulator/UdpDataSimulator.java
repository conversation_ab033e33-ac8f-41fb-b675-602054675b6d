package com.gy.show.simulator;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * UDP态势数据模拟器
 * 模拟无人机和无人艇的态势数据发送
 */
@Slf4j
public class UdpDataSimulator {

    private final String targetHost;
    private final int targetPort;
    private final int uavInterval;
    private final int shipInterval;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    private final SituationDataGenerator dataGenerator = new SituationDataGenerator();

    public UdpDataSimulator(String targetHost, int targetPort, int uavInterval, int shipInterval) {
        this.targetHost = targetHost;
        this.targetPort = targetPort;
        this.uavInterval = uavInterval;
        this.shipInterval = shipInterval;
    }

    // 默认构造函数，用于向后兼容
    public UdpDataSimulator() {
        this("localhost", 8080, 2, 3);
    }
    
    public static void main(String[] args) {
        UdpDataSimulator simulator = new UdpDataSimulator();
        simulator.startSimulation();
        
        // 运行10分钟后停止
        Runtime.getRuntime().addShutdownHook(new Thread(simulator::stopSimulation));
        
        try {
            Thread.sleep(600000); // 10分钟
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        simulator.stopSimulation();
    }
    
    public void startSimulation() {
        log.info("开始模拟态势数据发送到 {}:{}...", targetHost, targetPort);

        // 根据配置的间隔发送数据
        scheduler.scheduleAtFixedRate(this::sendUavData, 0, uavInterval, TimeUnit.SECONDS);
        scheduler.scheduleAtFixedRate(this::sendShipData, 1, shipInterval, TimeUnit.SECONDS);
    }
    
    public void stopSimulation() {
        log.info("停止模拟数据发送");
        scheduler.shutdown();
    }
    
    /**
     * 发送无人机态势数据
     */
    private void sendUavData() {
        try {
            // 生成模拟无人机数据
            List<SituationDataGenerator.UavData> uavList = dataGenerator.createMockUavData();
            updateUavPositions(uavList);

            // 生成数据包
            byte[] data = dataGenerator.generateUavSituationPacket(uavList);
            sendUdpPacket(data);

            log.info("发送无人机态势数据，目标数量: {}, 数据长度: {} 字节", uavList.size(), data.length);
            logUavData(uavList);
        } catch (Exception e) {
            log.error("发送无人机数据失败", e);
        }
    }

    /**
     * 发送无人艇态势数据
     */
    private void sendShipData() {
        try {
            // 生成模拟无人艇数据
            List<SituationDataGenerator.ShipData> shipList = dataGenerator.createMockShipData();
            updateShipPositions(shipList);

            // 生成数据包
            byte[] data = dataGenerator.generateShipSituationPacket(shipList);
            sendUdpPacket(data);

            log.info("发送无人艇态势数据，目标数量: {}, 数据长度: {} 字节", shipList.size(), data.length);
            logShipData(shipList);
        } catch (Exception e) {
            log.error("发送无人艇数据失败", e);
        }
    }
    
    /**
     * 更新无人机位置（模拟飞行轨迹）
     */
    private void updateUavPositions(List<SituationDataGenerator.UavData> uavList) {
        double time = System.currentTimeMillis() / 10000.0;

        for (int i = 0; i < uavList.size(); i++) {
            SituationDataGenerator.UavData uav = uavList.get(i);

            // 不同无人机使用不同的轨迹参数
            double phaseOffset = i * Math.PI / 2; // 相位偏移
            double radius = 0.01 + i * 0.005; // 不同半径

            // 圆形飞行轨迹
            double baseX = 116.3974 + i * 0.01;
            double baseY = 39.9093 + i * 0.01;

            uav.setLongitude(baseX + radius * Math.cos(time + phaseOffset));
            uav.setLatitude(baseY + radius * Math.sin(time + phaseOffset));

            // 高度变化
            uav.setRelativeAltitude(1000 + i * 200 + 100 * Math.sin(time * 2 + phaseOffset));
            uav.setGpsAltitude(uav.getRelativeAltitude() + 50);

            // 姿态变化
            uav.setYaw((time * 30 + i * 90) % 360);
            uav.setPitch(5 * Math.sin(time * 3 + phaseOffset));
            uav.setRoll(3 * Math.cos(time * 2 + phaseOffset));
        }
    }
    
    /**
     * 更新无人艇位置（模拟航行轨迹）
     */
    private void updateShipPositions(List<SituationDataGenerator.ShipData> shipList) {
        double time = System.currentTimeMillis() / 20000.0;

        for (int i = 0; i < shipList.size(); i++) {
            SituationDataGenerator.ShipData ship = shipList.get(i);

            // 不同无人艇使用不同的航行路线
            double baseX = 121.4737 + i * 0.01;
            double baseY = 31.2304 + i * 0.005;

            // 直线航行 + 小幅摆动
            ship.setLongitude(baseX + 0.001 * time + 0.0002 * Math.sin(time * 5));
            ship.setLatitude(baseY + 0.0005 * time + 0.0001 * Math.cos(time * 3));

            // 航向变化
            ship.setHeading((time * 10 + i * 45) % 360);
            ship.setSpeedDirection(ship.getHeading() + 5 * Math.sin(time * 2));

            // 姿态变化（海浪影响）
            ship.setRoll(2 * Math.sin(time * 8 + i));
            ship.setPitch(3 * Math.cos(time * 6 + i));

            // 舵角变化
            ship.setRudderAngle(10 * Math.sin(time * 1.5 + i));

            // 更新时间戳
            ship.setTimestamp(System.currentTimeMillis());
        }
    }
    
    /**
     * 记录无人机数据日志
     */
    private void logUavData(List<SituationDataGenerator.UavData> uavList) {
        for (SituationDataGenerator.UavData uav : uavList) {
            log.debug("无人机 {} - 位置: ({:.6f}, {:.6f}), 高度: {:.1f}m, 速度: {:.1f}m/s, 航向: {:.1f}°",
                uav.getName(),
                uav.getLongitude(),
                uav.getLatitude(),
                uav.getRelativeAltitude(),
                uav.getGroundSpeed(),
                uav.getYaw());
        }
    }

    /**
     * 记录无人艇数据日志
     */
    private void logShipData(List<SituationDataGenerator.ShipData> shipList) {
        for (SituationDataGenerator.ShipData ship : shipList) {
            log.debug("无人艇 {} - 位置: ({:.6f}, {:.6f}), 速度: {:.1f}节, 航向: {:.1f}°, 舵角: {:.1f}°",
                ship.getName(),
                ship.getLongitude(),
                ship.getLatitude(),
                ship.getSpeedMagnitude(),
                ship.getHeading(),
                ship.getRudderAngle());
        }
    }
    
    /**
     * 发送UDP数据包
     */
    private void sendUdpPacket(byte[] data) throws IOException {
        try (DatagramSocket socket = new DatagramSocket()) {
            InetAddress address = InetAddress.getByName(targetHost);
            DatagramPacket packet = new DatagramPacket(data, data.length, address, targetPort);
            socket.send(packet);
        }
    }
}
