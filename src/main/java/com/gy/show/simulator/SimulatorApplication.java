package com.gy.show.simulator;

import lombok.extern.slf4j.Slf4j;

import java.util.Scanner;

/**
 * 态势数据模拟器启动类
 */
@Slf4j
public class SimulatorApplication {

    public static void main(String[] args) {
        log.info("=== 态势数据模拟器 ===");
        log.info("本程序将模拟发送无人机和无人艇的态势数据");
        
        // 解析命令行参数
        String host = "localhost";
        int uavPort = 8080;      // 无人机端口
        int shipPort = 8081;     // 无人艇端口
        int uavInterval = 2;     // 无人机发送间隔（秒）
        int shipInterval = 3;    // 无人艇发送间隔（秒）

        if (args.length >= 1) {
            host = args[0];
        }
        if (args.length >= 2) {
            try {
                uavPort = Integer.parseInt(args[1]);
            } catch (NumberFormatException e) {
                log.warn("无人机端口号格式错误，使用默认端口: {}", uavPort);
            }
        }
        if (args.length >= 3) {
            try {
                shipPort = Integer.parseInt(args[2]);
            } catch (NumberFormatException e) {
                log.warn("无人艇端口号格式错误，使用默认端口: {}", shipPort);
            }
        }
        if (args.length >= 4) {
            try {
                uavInterval = Integer.parseInt(args[3]);
            } catch (NumberFormatException e) {
                log.warn("无人机发送间隔格式错误，使用默认值: {}秒", uavInterval);
            }
        }
        if (args.length >= 5) {
            try {
                shipInterval = Integer.parseInt(args[4]);
            } catch (NumberFormatException e) {
                log.warn("无人艇发送间隔格式错误，使用默认值: {}秒", shipInterval);
            }
        }
        
        log.info("配置参数:");
        log.info("  目标主机: {}", host);
        log.info("  无人机端口: {}", uavPort);
        log.info("  无人艇端口: {}", shipPort);
        log.info("  无人机发送间隔: {}秒", uavInterval);
        log.info("  无人艇发送间隔: {}秒", shipInterval);

        // 创建并启动模拟器
        UdpDataSimulator simulator = new UdpDataSimulator(host, uavPort, shipPort, uavInterval, shipInterval);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("正在停止模拟器...");
            simulator.stopSimulation();
        }));
        
        // 启动模拟器
        simulator.startSimulation();
        
        log.info("模拟器已启动，按 'q' + Enter 退出程序");
        
        // 等待用户输入退出命令
        Scanner scanner = new Scanner(System.in);
        while (true) {
            String input = scanner.nextLine().trim().toLowerCase();
            if ("q".equals(input) || "quit".equals(input) || "exit".equals(input)) {
                break;
            } else if ("status".equals(input) || "s".equals(input)) {
                printStatus(simulator);
            } else if ("help".equals(input) || "h".equals(input)) {
                printHelp();
            } else {
                log.info("未知命令: {}，输入 'help' 查看帮助", input);
            }
        }
        
        log.info("正在停止模拟器...");
        simulator.stopSimulation();
        log.info("模拟器已停止");
    }
    
    private static void printStatus(UdpDataSimulator simulator) {
        log.info("=== 模拟器状态 ===");
        log.info("运行状态: 正在运行");
        log.info("发送统计: 请查看上方日志");
        log.info("================");
    }
    
    private static void printHelp() {
        log.info("=== 可用命令 ===");
        log.info("q, quit, exit - 退出程序");
        log.info("s, status     - 显示状态");
        log.info("h, help       - 显示帮助");
        log.info("===============");
    }
}
