package com.gy.show.runnner;

import com.alibaba.fastjson.JSON;
import com.gy.show.constants.CacheConstant;
import com.gy.show.service.TerminalInfoService;
import com.gy.show.util.NodeStatusChecker;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class TaskMonitor implements ApplicationRunner {

    @Autowired
    private ThreadPoolTaskScheduler scheduler;

    @Autowired
    private TerminalInfoService terminalInfoService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 终端状态检测
        scheduler.scheduleAtFixedRate(this::checkTerminalStatus, 10000);

        // 初始化终端节点接入算法开关
        initNodeSelect();
    }

    private void initNodeSelect() {
        String result = RedisUtil.StringOps.get(CacheConstant.NODE_SELECT);

        // 如果为空进行初始化赋值操作
        if (StringUtils.isBlank(result)) {
            RedisUtil.StringOps.set(CacheConstant.NODE_SELECT, "1");
        }
    }

    private void checkTerminalStatus() {
        log.info("开始执行终端状态检查");

        // 查询当前终端状态
        Map<Integer, int[]> nodes = queryCurrentStatus();

        // 检查状态
        Map<Integer, Integer> nodeStatus = NodeStatusChecker.checkNodeStatus(nodes);
        log.info("终端节点检查结果：{}", nodeStatus);

        // 根据终端状态作出相应操作
        terminalOperation(nodeStatus);
    }

    private void terminalOperation(Map<Integer, Integer> nodeStatus) {
        for (Map.Entry<Integer, Integer> entry : nodeStatus.entrySet()) {
            Integer status = entry.getValue();
            if (status == 1) continue;

            // 在接入算法Terminal对象中增加节点状态参数
            if (status == 2) {
                log.info("终端：{}需要作切换", entry.getKey());
                RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_SINGLE_STATUS + entry.getKey(), status + "", 10, TimeUnit.SECONDS);
            }

            // 需要将数据库中对应终端状态改为未使用
            if (status == 3) {
                log.info("终端：{}未使用，将变更数据库结果", entry.getKey());
                terminalInfoService.updateTerminalStatus(entry.getKey(), 0);
            }
        }
    }

    private Map<Integer, int[]> queryCurrentStatus() {
        Map<Integer, int[]> nodes = new HashMap<>(6);
        for (int i = 0; i < 6; i++) {
            String node = RedisUtil.StringOps.get(CacheConstant.TERMINAL_ALL_STATUS + i);

            if (StringUtils.isBlank(node)) {
                // 如果为空说明当前终端没有上报状态，该数组为空
                nodes.put(i, new int[]{});
            } else {
                nodes.put(i, JSON.parseObject(node, int[].class));
            }
        }
        log.debug("获取到当前终端状态集合：{}", nodes);
        return nodes;
    }
}
