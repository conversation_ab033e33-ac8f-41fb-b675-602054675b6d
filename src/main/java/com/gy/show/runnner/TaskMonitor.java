package com.gy.show.runnner;

import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.entity.dto.TaskProgressDTO;
import com.gy.show.enums.TaskStatusEnum;
import com.gy.show.enums.TaskTypeEnum;
import com.gy.show.service.RequirementTaskService;
import com.gy.show.ws.FullViewTsServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@EnableScheduling
public class TaskMonitor implements ApplicationRunner {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private FullViewTsServer server;


    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("TaskMonitor 启动成功，开始监控任务进度");
    }

    /**
     * 定时推送任务进度到前端
     * 每5秒执行一次
     */
    @Scheduled(fixedRate = 5000)
    public void pushTaskProgress() {
        try {
            // 1.获取当前时间在任务开始时间和结束时间之间的任务集合
            List<RequirementTaskDTO> executingTasks = requirementTaskService.queryCurrentExecutingTasks();

            if (executingTasks.isEmpty()) {
                log.debug("当前没有正在执行的任务");
                return;
            }

            log.info("当前正在执行的任务数量: {}", executingTasks.size());

            // 2.根据当前时间来计算任务执行的百分比 (已在service层实现)
            // 3.将任务集合转换为TaskProgressDTO并组装推送数据
            List<TaskProgressDTO> taskProgressList = convertToTaskProgressDTO(executingTasks);

            // 4.通过websocket推送到前端 FULL_VIEW_TASK_PROGRESS
            server.sendTaskProgress(taskProgressList);

            // 打印任务进度信息
            for (TaskProgressDTO taskProgress : taskProgressList) {
                log.debug("任务[{}] 进度: {:.2f}%, 剩余时间: {}秒",
                    taskProgress.getTaskName(),
                    taskProgress.getProgressPercentage(),
                    taskProgress.getRemainingSeconds());
            }

        } catch (Exception e) {
            log.error("推送任务进度时发生异常", e);
        }
    }

    /**
     * 将RequirementTaskDTO转换为TaskProgressDTO
     */
    private List<TaskProgressDTO> convertToTaskProgressDTO(List<RequirementTaskDTO> tasks) {
        return tasks.stream().map(task -> {
            TaskProgressDTO progressDTO = new TaskProgressDTO();

            // 基本信息
            progressDTO.setTaskId(task.getId());
            progressDTO.setTaskName(task.getTaskName());
            progressDTO.setRequirementId(task.getRequirementId());
            progressDTO.setTaskType(task.getTaskType());
            progressDTO.setImportance(task.getImportance());
            progressDTO.setStatus(task.getStatus());
            progressDTO.setTaskComment(task.getTaskComment());

            // 时间信息
            progressDTO.setStartTime(task.getStartTime());
            progressDTO.setEndTime(task.getEndTime());
            progressDTO.setCurrentTime(task.getCurrentTime());

            // 进度信息
            progressDTO.setProgress(task.getProcess());
            progressDTO.setProgressPercentage(task.getProcess() * 100);

            // 计算时间相关信息
            calculateTimeInfo(progressDTO);

            // 设置状态和类型名称
            setDisplayNames(progressDTO);

            return progressDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 计算时间相关信息
     */
    private void calculateTimeInfo(TaskProgressDTO progressDTO) {
        LocalDateTime startTime = progressDTO.getStartTime();
        LocalDateTime endTime = progressDTO.getEndTime();
        LocalDateTime currentTime = progressDTO.getCurrentTime();

        // 计算总时长
        long totalSeconds = Duration.between(startTime, endTime).getSeconds();
        progressDTO.setTotalSeconds(totalSeconds);

        // 计算已执行时间
        long elapsedSeconds = Duration.between(startTime, currentTime).getSeconds();
        progressDTO.setElapsedSeconds(Math.max(0, elapsedSeconds));

        // 计算剩余时间
        long remainingSeconds = Duration.between(currentTime, endTime).getSeconds();
        progressDTO.setRemainingSeconds(Math.max(0, remainingSeconds));
    }

    /**
     * 设置显示名称
     */
    private void setDisplayNames(TaskProgressDTO progressDTO) {
        // 设置状态名称
        TaskStatusEnum statusEnum = TaskStatusEnum.getEnumByCode(progressDTO.getStatus());
        if (statusEnum != null) {
            progressDTO.setStatusName(statusEnum.getMessage());
        }

        // 设置任务类型名称
        if (progressDTO.getTaskType() != null && !progressDTO.getTaskType().isEmpty()) {
            List<String> typeNames = progressDTO.getTaskType().stream()
                .map(typeCode -> {
                    TaskTypeEnum typeEnum = TaskTypeEnum.getEnumByCode(typeCode);
                    return typeEnum != null ? typeEnum.getMessage() : "未知";
                })
                .collect(Collectors.toList());
            progressDTO.setTaskTypeName(String.join(",", typeNames));
        }
    }
}
