package com.gy.show.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 进度计算工具类
 */
public class ProgressCalculator {

    /**
     * 计算任务执行进度
     * @param startTime 任务开始时间
     * @param endTime 任务结束时间
     * @param currentTime 当前时间
     * @return 进度 (0.0 - 1.0)，保留4位小数
     */
    public static double calculateProgress(LocalDateTime startTime, LocalDateTime endTime, LocalDateTime currentTime) {
        if (currentTime.isBefore(startTime)) {
            return 0.0; // 任务未开始
        }
        
        if (currentTime.isAfter(endTime)) {
            return 1.0; // 任务已结束
        }
        
        // 计算总时长和已执行时长（以秒为单位）
        long totalDuration = Duration.between(startTime, endTime).getSeconds();
        long elapsedDuration = Duration.between(startTime, currentTime).getSeconds();
        
        if (totalDuration == 0) {
            return 1.0; // 避免除零错误
        }
        
        // 使用BigDecimal进行精确计算，保留4位小数
        BigDecimal elapsed = new BigDecimal(elapsedDuration);
        BigDecimal total = new BigDecimal(totalDuration);
        BigDecimal progress = elapsed.divide(total, 4, RoundingMode.HALF_UP);
        
        return progress.doubleValue();
    }
    
    /**
     * 将进度转换为百分比
     * @param progress 进度 (0.0 - 1.0)
     * @param scale 保留小数位数
     * @return 百分比，例如：23.43
     */
    public static double toPercentage(double progress, int scale) {
        BigDecimal progressDecimal = new BigDecimal(progress);
        BigDecimal percentage = progressDecimal.multiply(new BigDecimal(100));
        return percentage.setScale(scale, RoundingMode.HALF_UP).doubleValue();
    }
    
    /**
     * 将进度转换为百分比（默认保留2位小数）
     * @param progress 进度 (0.0 - 1.0)
     * @return 百分比，例如：23.43
     */
    public static double toPercentage(double progress) {
        return toPercentage(progress, 2);
    }
    
    /**
     * 格式化进度为百分比字符串
     * @param progress 进度 (0.0 - 1.0)
     * @param scale 保留小数位数
     * @return 格式化的百分比字符串，例如："23.43%"
     */
    public static String formatPercentage(double progress, int scale) {
        double percentage = toPercentage(progress, scale);
        return String.format("%." + scale + "f%%", percentage);
    }
    
    /**
     * 格式化进度为百分比字符串（默认保留2位小数）
     * @param progress 进度 (0.0 - 1.0)
     * @return 格式化的百分比字符串，例如："23.43%"
     */
    public static String formatPercentage(double progress) {
        return formatPercentage(progress, 2);
    }
}
