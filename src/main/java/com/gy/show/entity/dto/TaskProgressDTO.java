package com.gy.show.entity.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务进度数据传输对象
 */
@Data
public class TaskProgressDTO {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 需求ID
     */
    private String requirementId;
    
    /**
     * 任务类型
     */
    private List<Integer> taskType;
    
    /**
     * 任务类型名称
     */
    private String taskTypeName;
    
    /**
     * 重要程度 1 一般 2 重要
     */
    private Integer importance;
    
    /**
     * 任务状态 0 待执行 1 执行中 2 执行成功 3 执行失败 4 已取消
     */
    private Integer status;
    
    /**
     * 任务状态名称
     */
    private String statusName;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 当前时间
     */
    private LocalDateTime currentTime;
    
    /**
     * 任务执行进度 (0.0 - 1.0)
     */
    private Double progress;
    
    /**
     * 任务执行进度百分比 (0 - 100)
     */
    private Double progressPercentage;
    
    /**
     * 剩余时间（秒）
     */
    private Long remainingSeconds;
    
    /**
     * 已执行时间（秒）
     */
    private Long elapsedSeconds;
    
    /**
     * 总时长（秒）
     */
    private Long totalSeconds;
    
    /**
     * 任务描述
     */
    private String taskComment;
}
