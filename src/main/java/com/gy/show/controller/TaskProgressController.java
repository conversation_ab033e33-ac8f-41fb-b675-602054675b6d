package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.service.RequirementTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 任务进度控制器
 */
@Api(tags = "任务进度管理")
@RestController
@RequestMapping("/api/task-progress")
@Slf4j
public class TaskProgressController {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @ApiOperation("获取当前正在执行的任务进度")
    @GetMapping("/current")
    public Result getCurrentExecutingTasks() {
        try {
            List<RequirementTaskDTO> executingTasks = requirementTaskService.queryCurrentExecutingTasks();
            return Result.ok(executingTasks);
        } catch (Exception e) {
            log.error("获取当前执行任务失败", e);
            return Result.error("获取当前执行任务失败: " + e.getMessage());
        }
    }
}
