package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.runnner.TaskMonitor;
import com.gy.show.service.RequirementTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 任务进度控制器
 */
@Api(tags = "任务进度管理")
@RestController
@RequestMapping("/api/task-progress")
@Slf4j
public class TaskProgressController {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @ApiOperation("获取当前正在执行的任务进度")
    @GetMapping("/current")
    public Result getCurrentExecutingTasks() {
        try {
            List<RequirementTaskDTO> executingTasks = requirementTaskService.queryCurrentExecutingTasks();
            return Result.ok(executingTasks);
        } catch (Exception e) {
            log.error("获取当前执行任务失败", e);
            return Result.error("获取当前执行任务失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取所有需要状态更新的任务")
    @GetMapping("/status-update")
    public Result getTasksForStatusUpdate() {
        try {
            List<RequirementTaskDTO> tasks = requirementTaskService.queryTasksForStatusUpdate();
            return Result.ok(tasks);
        } catch (Exception e) {
            log.error("获取需要状态更新的任务失败", e);
            return Result.error("获取需要状态更新的任务失败: " + e.getMessage());
        }
    }

    @ApiOperation("手动更新任务状态")
    @PostMapping("/update-status")
    public Result updateTaskStatus(@RequestParam List<String> taskIds, @RequestParam Integer status) {
        try {
            requirementTaskService.batchUpdateTaskStatus(taskIds, status);
            return Result.ok("任务状态更新成功");
        } catch (Exception e) {
            log.error("更新任务状态失败", e);
            return Result.error("更新任务状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("手动触发任务状态自动更新")
    @PostMapping("/auto-update-status")
    public Result autoUpdateTaskStatus() {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<RequirementTaskDTO> allTasks = requirementTaskService.queryTasksForStatusUpdate();

            List<String> tasksToStart = new ArrayList<>();
            List<String> tasksToComplete = new ArrayList<>();

            for (RequirementTaskDTO task : allTasks) {
                if (task.getStatus() == 0 && !now.isBefore(task.getStartTime())) {
                    tasksToStart.add(task.getId());
                } else if (task.getStatus() == 1 && now.isAfter(task.getEndTime())) {
                    tasksToComplete.add(task.getId());
                }
            }

            if (!tasksToStart.isEmpty()) {
                requirementTaskService.batchUpdateTaskStatus(tasksToStart, 1);
            }

            if (!tasksToComplete.isEmpty()) {
                requirementTaskService.batchUpdateTaskStatus(tasksToComplete, 2);
            }

            return Result.ok(String.format("状态更新完成，启动任务: %d个，完成任务: %d个",
                tasksToStart.size(), tasksToComplete.size()));
        } catch (Exception e) {
            log.error("自动更新任务状态失败", e);
            return Result.error("自动更新任务状态失败: " + e.getMessage());
        }
    }
}
