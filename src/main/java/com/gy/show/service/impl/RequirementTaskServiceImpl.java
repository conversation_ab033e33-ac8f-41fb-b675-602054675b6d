package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dos.RequirementInfo;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dos.TaskTargetRelation;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.entity.dto.TaskTargetRelationDTO;
import com.gy.show.entity.vo.TaskQueryVO;
import com.gy.show.enums.RepeatTypeEnum;
import com.gy.show.enums.TaskScheduleStatusEnum;
import com.gy.show.mapper.RequirementTaskMapper;
import com.gy.show.service.DataEquipmentOccupancyService;
import com.gy.show.service.RequirementInfoService;
import com.gy.show.service.RequirementTaskService;
import com.gy.show.service.TaskTargetService;
import com.gy.show.util.ConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RequirementTaskServiceImpl extends ServiceImpl<RequirementTaskMapper, RequirementTask> implements RequirementTaskService {

    @Autowired
    private RequirementInfoService requirementInfoService;

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @Autowired
    private TaskTargetService taskTargetService;

    @Override
    public IPage getAllRequirementTasks(IPage page, TaskQueryVO taskQueryVO) {
        LambdaQueryWrapper<RequirementTask> queryWrapper = Wrappers.<RequirementTask>lambdaQuery()
                .like(StringUtils.isNotBlank(taskQueryVO.getKeyword()), RequirementTask::getTaskName, taskQueryVO.getKeyword())
                .in(CollUtil.isNotEmpty(taskQueryVO.getRequirementIds()), RequirementTask::getRequirementId, taskQueryVO.getRequirementIds())
                .ge(taskQueryVO.getStartTime() != null, RequirementTask::getEndTime, taskQueryVO.getStartTime())
                .le(taskQueryVO.getEndTime() != null, RequirementTask::getStartTime, taskQueryVO.getEndTime())
                .orderByAsc(RequirementTask::getStartTime);

        IPage<RequirementTask> taskIPage = page(page, queryWrapper);
        return ConvertUtil.buildPage(taskIPage);
    }

    @Override
    public RequirementTaskDTO getRequirementTaskById(String id) {
        RequirementTask task = getById(id);

        RequirementTaskDTO taskDTO = task.convert();

        TaskTargetRelation targetRelation = taskTargetService.getById(task.getTargetRelationId());

        taskDTO.setTarget(targetRelation.convert());

        return taskDTO;
    }

    @Override
    public Object getDataByTarget(String targetRelationId) {
        List<RequirementTask> tasks = list(Wrappers.<RequirementTask>lambdaQuery().eq(RequirementTask::getTargetRelationId, targetRelationId));
        List<RequirementTaskDTO> taskDTOS = tasks.stream()
                .map(RequirementTask::convert)
                .collect(Collectors.toList());

        List<String> taskIds = tasks.stream()
                .map(RequirementTask::getId)
                .collect(Collectors.toList());

        List<DataEquipmentOccupancy> dataEquipmentOccupancies = dataEquipmentOccupancyService.list(Wrappers.<DataEquipmentOccupancy>lambdaQuery().in(DataEquipmentOccupancy::getTaskId, taskIds));
        List<String> equipmentIds = dataEquipmentOccupancies.stream()
                .map(DataEquipmentOccupancy::getEquipmentId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();

        result.put("task", taskDTOS);
        result.put("equipment", equipmentIds);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RequirementTask addRequirementTask(RequirementTaskDTO requirementTaskDTO) {
        RequirementTask task = requirementTaskDTO.convert();
        save(task);
        return task;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRequirementTask(RequirementTaskDTO requirementTaskDTO) {
        RequirementTask task = requirementTaskDTO.convert();

        RequirementInfoDTO info = requirementInfoService.getRequirementInfoById(requirementTaskDTO.getRequirementId());
        // 检查任务开始和结束时间是否在需求时间范围内
        if (info != null) {
            if (task.getStartTime().isBefore(info.getStartTime()) || task.getEndTime().isAfter(info.getEndTime())) {
                throw new ServiceException("任务的开始时间或结束时间不能超出需求时间");
            }
        }

        // 检查任务开始和结束时间是否在航迹时间范围内
        TaskTargetRelationDTO target = requirementTaskDTO.getTarget();
        if (target != null) {
            if (task.getStartTime().isBefore(target.getStartTime()) || task.getEndTime().isAfter(target.getEndTime())) {
                throw new ServiceException("任务的开始时间或结束时间不能超出航迹时间");
            }
        }

        // 检查任务名称是否存在
        int count = count(Wrappers.<RequirementTask>lambdaQuery().eq(RequirementTask::getTaskName, requirementTaskDTO.getTaskName())
                .ne(RequirementTask::getId, requirementTaskDTO.getId())
                .eq(RequirementTask::getRepeatType, RepeatTypeEnum.ONCE.getCode())
                .eq(RequirementTask::getRequirementId, requirementTaskDTO.getRequirementId()));

        if (count > 0) {
            throw new ServiceException("任务名称已存在，请重新输入");
        }

        updateById(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitTask(String requirementId) {
        RequirementInfo info = requirementInfoService.getById(requirementId);
//        info.setSubmitStatus(RequirementStatusEnum.DECOMPOSED.getCode());

        requirementInfoService.updateById(info);
    }

    @Override
    public List<RequirementTask> getTaskByRequirement(String requirementId) {
        return list(Wrappers.<RequirementTask>lambdaQuery().eq(RequirementTask::getRequirementId, requirementId));
    }

    @Override
    public Object queryTaskByEquipment(String generalId, String equipmentId) {
        List<DataEquipmentOccupancy> relations = dataEquipmentOccupancyService.list(Wrappers.<DataEquipmentOccupancy>lambdaQuery().eq(DataEquipmentOccupancy::getGeneralId, generalId)
                .eq(DataEquipmentOccupancy::getEquipmentId, equipmentId));

        List<String> taskIds = relations.stream()
                .map(DataEquipmentOccupancy::getTaskId)
                .collect(Collectors.toList());

        List<RequirementTaskDTO> tasks = new ArrayList<>();
        if (CollUtil.isNotEmpty(taskIds)) {
            Collection<RequirementTask> requirementTasks = listByIds(taskIds);

            tasks = requirementTasks.stream()
                    .map(RequirementTask::convert)
                    .collect(Collectors.toList());
        }
        return tasks;
    }

    @Override
    public List<RequirementTaskDTO> queryScheduleTask() {
        // 查询周期性需求任务
        List<RequirementInfo> infos = requirementInfoService.list(Wrappers.<RequirementInfo>lambdaQuery().eq(RequirementInfo::getScheduleType, 2));

        List<RequirementTaskDTO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(infos)) {
            List<String> ids = infos.stream()
                    .map(RequirementInfo::getId)
                    .collect(Collectors.toList());

            // 查询不属于已调度的任务
            List<RequirementTask> tasks = list(Wrappers.<RequirementTask>lambdaQuery().in(RequirementTask::getRequirementId, ids)
                    .eq(RequirementTask::getScheduleStatus, TaskScheduleStatusEnum.NOT_SCHEDULE.getCode()));

            result = tasks.stream()
                    .map(RequirementTask::convert)
                    .sorted(Comparator.comparing(RequirementTaskDTO::getStatus))
                    .collect(Collectors.toList());
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void changeTaskStatus(List<RequirementTaskDTO> tasks, TaskScheduleStatusEnum taskStatusEnum) {
        if (CollUtil.isEmpty(tasks)) return;
        List<RequirementTask> taskList = tasks.stream()
                .map(t -> {
                    RequirementTask task = t.convert();
                    task.setScheduleStatus(taskStatusEnum.getCode());

                    return task;
                })
                .collect(Collectors.toList());

        saveOrUpdateBatch(taskList);
    }

    @Override
    public List<RequirementTaskDTO> queryCurrentExecutingTasks() {
        LocalDateTime now = LocalDateTime.now();

        // 查询当前时间在任务开始时间和结束时间之间的任务
        List<RequirementTask> tasks = list(Wrappers.<RequirementTask>lambdaQuery()
                .le(RequirementTask::getStartTime, now)  // 开始时间 <= 当前时间
                .ge(RequirementTask::getEndTime, now)    // 结束时间 >= 当前时间
                .eq(RequirementTask::getStatus, 1));     // 状态为执行中

        return tasks.stream()
                .map(task -> {
                    RequirementTaskDTO taskDTO = task.convert();
                    taskDTO.setCurrentTime(now);

                    // 计算任务执行进度百分比
                    double progress = calculateTaskProgress(task.getStartTime(), task.getEndTime(), now);
                    taskDTO.setProcess(progress);

                    return taskDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算任务执行进度百分比
     * @param startTime 任务开始时间
     * @param endTime 任务结束时间
     * @param currentTime 当前时间
     * @return 进度百分比 (0.0 - 1.0)
     */
    private double calculateTaskProgress(LocalDateTime startTime, LocalDateTime endTime, LocalDateTime currentTime) {
        if (currentTime.isBefore(startTime)) {
            return 0.0; // 任务未开始
        }

        if (currentTime.isAfter(endTime)) {
            return 1.0; // 任务已结束
        }

        // 计算总时长和已执行时长（以秒为单位）
        long totalDuration = java.time.Duration.between(startTime, endTime).getSeconds();
        long elapsedDuration = java.time.Duration.between(startTime, currentTime).getSeconds();

        if (totalDuration == 0) {
            return 1.0; // 避免除零错误
        }

        return (double) elapsedDuration / totalDuration;
    }
}
