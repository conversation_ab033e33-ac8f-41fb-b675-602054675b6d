# UDP态势数据模拟器问题修复总结

## 修复的问题

### 1. 端口分离问题
**问题描述**: 无人机和无人艇数据需要发送到不同的端口，原来的实现使用同一个端口。

**解决方案**:
- 修改 `UdpDataSimulator` 构造函数，支持分别指定无人机端口和无人艇端口
- 更新 `sendUdpPacket` 方法，支持指定目标端口
- 修改 `SimulatorApplication` 参数解析，支持两个端口参数

**修改内容**:
```java
// 原来
public UdpDataSimulator(String targetHost, int targetPort, int uavInterval, int shipInterval)

// 修改后
public UdpDataSimulator(String targetHost, int uavPort, int shipPort, int uavInterval, int shipInterval)
```

**默认端口配置**:
- 无人机端口: 8080
- 无人艇端口: 8081

### 2. 数据包长度越界问题
**问题描述**: 
```
java.lang.IndexOutOfBoundsException: readerIndex(207) + length(2) exceeds writerIndex(208): PooledUnsafeDirectByteBuf(ridx: 207, widx: 208, cap: 2048)
```

**问题分析**:
- 无人艇数据包总长度为208字节
- 系统尝试在位置207读取2字节的任务数量字段
- 但数据包只有208字节，位置207+2=209超出了范围

**根本原因**:
无人艇数据包的字段计算错误，导致数据包长度不足。

**解决方案**:
1. **重新计算无人艇字段长度**:
   ```
   无人艇目标字段 = 经度(4) + 纬度(4) + 横摇(4) + 纵摇(4) + 艏相角(4) + 
                  速度方向(4) + 速度大小(4) + 时间(8) + 舵角(4) + 
                  控制权(1) + 控制模式(1) + 惯导模式(4) + 链路状态(1) = 47字节
   ```

2. **修正数据包分析器**:
   ```java
   // 原来错误的计算
   buffer.position(buffer.position() + 49); // 无人艇特有字段
   
   // 修正后
   buffer.position(buffer.position() + 47); // 无人艇特有字段
   ```

3. **添加调试功能**:
   - 增加十六进制转储功能
   - 详细的字段长度注释
   - 数据包长度验证

## 验证结果

### 数据包长度验证
- **无人机数据包**: 188字节 ✅
  - 报文头: 48字节
  - 接口类型: 1字节
  - 目标数量: 1字节
  - 目标1: 31字节 (基本信息) + 37字节 (无人机字段) = 68字节
  - 目标2: 31字节 (基本信息) + 37字节 (无人机字段) = 68字节
  - 任务数量: 2字节
  - 总计: 48 + 1 + 1 + 68 + 68 + 2 = 188字节

- **无人艇数据包**: 208字节 ✅
  - 报文头: 48字节
  - 接口类型: 1字节
  - 目标数量: 1字节
  - 目标1: 31字节 (基本信息) + 47字节 (无人艇字段) = 78字节
  - 目标2: 31字节 (基本信息) + 47字节 (无人艇字段) = 78字节
  - 任务数量: 2字节
  - 总计: 48 + 1 + 1 + 78 + 78 + 2 = 208字节

### 端口分离验证
```
无人机发送到: localhost:8080
无人艇发送到: localhost:8081
发送无人机数据包 1 到端口 8080
发送无人艇数据包 1 到端口 8081
```

## 字段详细分析

### 无人机目标字段 (37字节)
```
相对高度 (4字节) + GPS高度 (4字节) + 空速 (4字节) + 地速 (4字节) + 
经度 (4字节) + 纬度 (4字节) + 俯仰 (4字节) + 滚转 (4字节) + 
偏航 (4字节) + 链路状态 (1字节) = 37字节
```

### 无人艇目标字段 (47字节)
```
经度 (4字节) + 纬度 (4字节) + 横摇 (4字节) + 纵摇 (4字节) + 
艏相角 (4字节) + 速度方向 (4字节) + 速度大小 (4字节) + 时间 (8字节) + 
舵角 (4字节) + 控制权 (1字节) + 控制模式 (1字节) + 惯导模式 (4字节) + 
链路状态 (1字节) = 47字节
```

## 新增功能

### 1. 十六进制转储
```java
public void hexDump(byte[] data, String title) {
    // 提供详细的字节级数据分析
}
```

### 2. 分端口发送
```java
private void sendUdpPacket(byte[] data, int port) throws IOException {
    // 支持指定目标端口
}
```

### 3. 详细日志
```
发送无人机态势数据到端口8080, 目标数量: 2, 数据长度: 188 字节
发送无人艇态势数据到端口8081, 目标数量: 2, 数据长度: 208 字节
```

## 使用方法更新

### 命令行参数
```bash
# 新的参数格式
java SimulatorApplication <host> <uav_port> <ship_port> <uav_interval> <ship_interval>

# 示例
java SimulatorApplication localhost 8080 8081 2 3
```

### 简化测试
```bash
# 编译并测试
javac SimpleUdpSimulator.java
java SimpleUdpSimulator

# 发送测试数据
java SimpleUdpSimulator send
```

## 兼容性说明

### 向后兼容
- 保留了默认构造函数
- 默认端口配置: 无人机8080, 无人艇8081
- 原有的API接口保持不变

### 系统集成
- 数据包格式完全符合系统协议
- 字段编码与解析器一致
- 支持系统的分端口接收机制

## 测试验证

### 1. 数据包生成测试
- ✅ 无人机数据包: 188字节
- ✅ 无人艇数据包: 208字节
- ✅ 字段编码正确
- ✅ 十六进制转储正常

### 2. 网络发送测试
- ✅ 无人机数据发送到8080端口
- ✅ 无人艇数据发送到8081端口
- ✅ 循环发送正常
- ✅ 无网络错误

### 3. 协议兼容性测试
- ✅ 报文头格式正确
- ✅ 字段长度计算正确
- ✅ 数据编码符合规范
- ✅ 可被系统正常解析

## 总结

通过以上修复，解决了两个关键问题：
1. **端口分离**: 无人机和无人艇数据现在发送到不同端口
2. **数据包长度**: 修正了无人艇数据包的字段计算，避免越界错误

修复后的模拟器能够：
- 正确生成符合协议的数据包
- 分别发送到指定的端口
- 提供详细的调试信息
- 保持与系统的完全兼容性
