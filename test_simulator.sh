#!/bin/bash

# UDP态势数据模拟器测试脚本

echo "=== UDP态势数据模拟器测试 ==="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请先安装Java"
    exit 1
fi

echo "Java版本:"
java -version

# 设置类路径
CLASSPATH=".:src/main/java"

# 编译Java文件
echo ""
echo "正在编译Java文件..."
find src/main/java/com/gy/show/simulator -name "*.java" -exec javac -cp "$CLASSPATH" {} \;

if [ $? -eq 0 ]; then
    echo "编译成功"
else
    echo "编译失败"
    exit 1
fi

# 检查是否有参数
if [ $# -eq 0 ]; then
    echo ""
    echo "使用默认参数启动模拟器..."
    echo "目标: localhost:8080"
    echo "无人机间隔: 2秒"
    echo "无人艇间隔: 3秒"
    echo ""
    echo "按Ctrl+C停止模拟器"
    echo ""
    
    java -cp "$CLASSPATH" com.gy.show.simulator.SimulatorApplication
else
    echo ""
    echo "使用自定义参数启动模拟器..."
    echo "参数: $@"
    echo ""
    echo "按Ctrl+C停止模拟器"
    echo ""
    
    java -cp "$CLASSPATH" com.gy.show.simulator.SimulatorApplication "$@"
fi

echo ""
echo "模拟器已停止"
