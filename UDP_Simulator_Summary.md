# UDP态势数据模拟器实现总结

## 项目概述

根据系统中的ControlMessageDecodeHandler解析逻辑，成功实现了完整的UDP态势数据模拟器，能够生成符合协议规范的无人机和无人艇态势数据包，并通过UDP协议循环发送。

## 实现的文件

### 1. 核心模拟器文件
- `src/main/java/com/gy/show/simulator/UdpDataSimulator.java` - 主要的UDP发送器
- `src/main/java/com/gy/show/simulator/SituationDataGenerator.java` - 数据包生成器
- `src/main/java/com/gy/show/simulator/SimulatorApplication.java` - 启动应用
- `src/main/java/com/gy/show/simulator/PacketAnalyzer.java` - 数据包分析工具

### 2. 测试和工具文件
- `SimpleUdpSimulator.java` - 简化版测试器（不依赖外部库）
- `test_simulator.sh` - 测试脚本
- `UDP_Simulator_README.md` - 详细使用说明

## 协议实现详情

### 报文头结构 (48字节)
```
字段名称     | 长度  | 说明
------------|-------|------------------
包序号      | 2字节 | 随机生成的包序号
信源        | 2字节 | 0x1001
信宿        | 2字节 | 0x2001  
包类型      | 1字节 | 0x04(无人机) / 0x03(无人艇)
包长度      | 2字节 | 报文体长度
分段数      | 1字节 | 0x01
分段号      | 1字节 | 0x01
预留字段    | 37字节| 全零填充
```

### 无人机态势数据结构
```
接口类型 (1字节) = 0x02
目标数量 (1字节) = 2
[目标数据块] × 2:
  - 目标序号 (1字节)
  - 目标类型 (2字节) = 0x04
  - 目标代号 (8字节)
  - 目标名称 (20字节)
  - 相对高度 (4字节) = 实际值 × 100
  - GPS高度 (4字节) = 实际值 × 100
  - 空速 (4字节) = 实际值 × 100
  - 地速 (4字节) = 实际值 × 100
  - 经度 (4字节) = 实际值 × 10,000,000
  - 纬度 (4字节) = 实际值 × 10,000,000
  - 俯仰 (4字节) = 实际值 × 100
  - 滚转 (4字节) = 实际值 × 100
  - 偏航 (4字节) = 实际值 × 100
  - 链路状态 (1字节) = 位字段
执行任务数量 (2字节) = 1
```

### 无人艇态势数据结构
```
接口类型 (1字节) = 0x02
目标数量 (1字节) = 2
[目标数据块] × 2:
  - 目标序号 (1字节)
  - 目标类型 (2字节) = 0x03
  - 目标代号 (8字节)
  - 目标名称 (20字节)
  - 经度 (4字节) = 实际值 × 10,000,000
  - 纬度 (4字节) = 实际值 × 10,000,000
  - 横摇 (4字节) = 实际值 × 10
  - 纵摇 (4字节) = 实际值 × 10
  - 艏相角 (4字节) = 实际值 × 10
  - 速度方向 (4字节) = 实际值 × 10
  - 速度大小 (4字节) = 实际值 × 10
  - 时间 (8字节) = 时间戳
  - 舵角 (4字节) = 实际值 × 1000 ÷ 30
  - 控制权 (1字节)
  - 控制模式 (1字节)
  - 惯导模式 (4字节)
  - 链路状态 (1字节) = 位字段
执行任务数量 (2字节) = 1
```

## 模拟数据特性

### 无人机模拟 (2个目标)
1. **UAV-Alpha (代号: 1001)**
   - 位置: 北京地区 (116.3974°, 39.9093°)
   - 轨迹: 圆形飞行，半径约1公里
   - 高度: 1000m ± 100m 动态变化
   - 速度: 空速80m/s, 地速75m/s (带随机扰动)

2. **UAV-Beta (代号: 1002)**
   - 位置: 北京地区 (116.4074°, 39.9193°)
   - 轨迹: 圆形飞行，不同相位
   - 高度: 1200m ± 100m 动态变化
   - 速度: 空速85m/s, 地速80m/s (带随机扰动)

### 无人艇模拟 (2个目标)
1. **SHIP-Alpha (代号: 2001)**
   - 位置: 上海地区 (121.4737°, 31.2304°)
   - 轨迹: 直线航行 + 海浪摆动
   - 速度: 15节 ± 2节
   - 姿态: 模拟海浪影响的横摇纵摇

2. **SHIP-Beta (代号: 2002)**
   - 位置: 上海地区 (121.4837°, 31.2404°)
   - 轨迹: 平行航线
   - 速度: 12节 ± 2节
   - 姿态: 不同的摆动模式

## 使用方法

### 1. 快速测试
```bash
# 编译并运行简化版测试器
javac SimpleUdpSimulator.java
java SimpleUdpSimulator

# 发送测试数据
java SimpleUdpSimulator send
```

### 2. 完整版本（需要项目环境）
```bash
# 使用测试脚本
./test_simulator.sh

# 或直接运行
java -cp ".:src/main/java" com.gy.show.simulator.SimulatorApplication localhost 8080 2 3
```

### 3. 参数说明
- 第1个参数: 目标主机 (默认: localhost)
- 第2个参数: 目标端口 (默认: 8080)
- 第3个参数: 无人机发送间隔秒数 (默认: 2)
- 第4个参数: 无人艇发送间隔秒数 (默认: 3)

## 测试结果

### 数据包大小
- **无人机数据包**: 188字节 (包含2个目标)
- **无人艇数据包**: 208字节 (包含2个目标)

### 字段验证
- ✅ 报文头格式正确 (48字节)
- ✅ 接口类型正确 (0x02 = 态势数据)
- ✅ 目标数量正确 (2个目标)
- ✅ 目标类型正确 (0x04 = 无人机, 0x03 = 无人艇)
- ✅ 字段编码符合协议规范

## 扩展功能

### 1. 数据包分析
- 提供完整的数据包解析功能
- 支持字段级别的详细分析
- 验证数据编码正确性

### 2. 轨迹模拟
- 无人机: 圆形飞行轨迹，支持多机编队
- 无人艇: 直线航行轨迹，支持海浪模拟
- 实时位置、姿态、速度更新

### 3. 状态模拟
- 链路连接状态变化
- 控制模式切换
- 设备状态模拟

## 与系统集成

### 1. 协议兼容性
- 完全按照ControlMessageDecodeHandler的解析逻辑构建
- 字段编码与FieldDefinition类一致
- 支持ExternalDataTypeEnum定义的数据类型

### 2. 数据流程
```
模拟器生成数据 → UDP发送 → 系统接收 → ControlMessageDecodeHandler解析 → ExternalDataService处理
```

### 3. 验证方法
- 通过系统日志验证数据接收
- 检查前端态势显示
- 对比解析结果与模拟数据

## 注意事项

1. **网络配置**: 确保目标端口与系统监听端口一致
2. **字节序**: 数据包使用大端字节序（Big Endian）
3. **编码精度**: 严格按照字段定义进行数值编码
4. **发送频率**: 避免过高频率影响系统性能
5. **数据一致性**: 确保模拟数据的逻辑合理性

## 故障排除

### 常见问题
- **编译错误**: 检查Java环境和类路径
- **连接失败**: 验证网络连通性和端口配置
- **数据格式错误**: 使用PacketAnalyzer验证数据包格式
- **解析失败**: 检查字段编码和字节序

### 调试建议
1. 使用SimpleUdpSimulator进行基础测试
2. 启用详细日志查看数据包内容
3. 使用网络抓包工具验证UDP传输
4. 对比系统解析结果与预期数据

这个UDP态势数据模拟器完全按照系统协议规范实现，能够生成标准的态势数据包，为系统测试和开发提供可靠的数据源。
