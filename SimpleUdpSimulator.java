import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Random;

/**
 * 简化版UDP态势数据模拟器
 * 用于测试数据包生成和发送功能
 */
public class SimpleUdpSimulator {

    private static final String TARGET_HOST = "localhost";
    private static final int UAV_PORT = 8080;    // 无人机端口
    private static final int SHIP_PORT = 8081;   // 无人艇端口
    private final Random random = new Random();

    public static void main(String[] args) {
        SimpleUdpSimulator simulator = new SimpleUdpSimulator();
        
        System.out.println("=== UDP态势数据模拟器测试 ===");
        
        try {
            // 生成并分析无人机数据包
            byte[] uavData = simulator.generateUavPacket();
            System.out.println("生成无人机数据包，长度: " + uavData.length + " 字节");
            simulator.analyzePacket(uavData, "无人机");
            
            System.out.println();
            
            // 生成并分析无人艇数据包
            byte[] shipData = simulator.generateShipPacket();
            System.out.println("生成无人艇数据包，长度: " + shipData.length + " 字节");
            simulator.analyzePacket(shipData, "无人艇");
            
            // 发送测试（可选）
            if (args.length > 0 && "send".equals(args[0])) {
                System.out.println("\n开始发送测试数据...");
                simulator.sendTestData();
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成无人机数据包
     */
    public byte[] generateUavPacket() {
        ByteBuffer buffer = ByteBuffer.allocate(1024);
        
        // 构建报文头 (48字节)
        buildHeader(buffer, (byte) 0x04); // 无人机数据类型
        
        // 接口类型 (1字节) - 0x02表示态势数据
        buffer.put((byte) 0x02);
        
        // 目标数量 (1字节)
        buffer.put((byte) 0x02); // 2个无人机
        
        // 无人机1
        buildUavTarget(buffer, 1, 1001L, "UAV-Alpha", 116.3974, 39.9093, 1000.0);
        
        // 无人机2
        buildUavTarget(buffer, 2, 1002L, "UAV-Beta", 116.4074, 39.9193, 1200.0);
        
        // 执行任务数量 (2字节)
        buffer.putShort((short) 1);
        
        return finalizePacket(buffer);
    }

    /**
     * 生成无人艇数据包
     */
    public byte[] generateShipPacket() {
        ByteBuffer buffer = ByteBuffer.allocate(1024);
        
        // 构建报文头 (48字节)
        buildHeader(buffer, (byte) 0x03); // 无人艇数据类型
        
        // 接口类型 (1字节) - 0x02表示态势数据
        buffer.put((byte) 0x02);
        
        // 目标数量 (1字节)
        buffer.put((byte) 0x02); // 2个无人艇
        
        // 无人艇1
        buildShipTarget(buffer, 1, 2001L, "SHIP-Alpha", 121.4737, 31.2304);
        
        // 无人艇2
        buildShipTarget(buffer, 2, 2002L, "SHIP-Beta", 121.4837, 31.2404);
        
        // 执行任务数量 (2字节)
        buffer.putShort((short) 1);
        
        return finalizePacket(buffer);
    }

    /**
     * 构建报文头
     */
    private void buildHeader(ByteBuffer buffer, byte packageType) {
        // 包序号 (2字节)
        buffer.putShort((short) (random.nextInt(65535) + 1));
        
        // 信源 (2字节)
        buffer.putShort((short) 0x1001);
        
        // 信宿 (2字节)
        buffer.putShort((short) 0x2001);
        
        // 包类型 (1字节)
        buffer.put(packageType);
        
        // 包长度 (2字节) - 占位，后续更新
        buffer.putShort((short) 0);
        
        // 分段数 (1字节)
        buffer.put((byte) 0x01);
        
        // 分段号 (1字节)
        buffer.put((byte) 0x01);
        
        // 预留字段 (37字节)
        buffer.put(new byte[37]);
    }

    /**
     * 构建无人机目标数据
     */
    private void buildUavTarget(ByteBuffer buffer, int seq, long code, String name, 
                               double longitude, double latitude, double altitude) {
        // 目标序号 (1字节)
        buffer.put((byte) seq);
        
        // 目标类型 (2字节)
        buffer.putShort((short) 0x04);
        
        // 目标代号 (8字节)
        buffer.putLong(code);
        
        // 目标名称 (20字节)
        writeFixedString(buffer, name, 20);
        
        // 相对高度 (4字节) - 乘以100
        buffer.putInt((int) (altitude * 100));
        
        // GPS高度 (4字节)
        buffer.putInt((int) ((altitude + 50) * 100));
        
        // 空速 (4字节)
        buffer.putInt((int) (80.0 * 100 + random.nextGaussian() * 5));
        
        // 地速 (4字节)
        buffer.putInt((int) (75.0 * 100 + random.nextGaussian() * 5));
        
        // 经度 (4字节) - 乘以10000000
        buffer.putInt((int) (longitude * 10000000));
        
        // 纬度 (4字节)
        buffer.putInt((int) (latitude * 10000000));
        
        // 俯仰 (4字节)
        buffer.putInt((int) (random.nextGaussian() * 5 * 100));
        
        // 滚转 (4字节)
        buffer.putInt((int) (random.nextGaussian() * 3 * 100));
        
        // 偏航 (4字节)
        buffer.putInt((int) (random.nextDouble() * 360 * 100));
        
        // 链路连接状态 (1字节)
        buffer.put((byte) 0x03); // 电台和测控链路都连接
    }

    /**
     * 构建无人艇目标数据
     */
    private void buildShipTarget(ByteBuffer buffer, int seq, long code, String name,
                                double longitude, double latitude) {
        // 目标序号 (1字节)
        buffer.put((byte) seq);
        
        // 目标类型 (2字节)
        buffer.putShort((short) 0x03);
        
        // 目标代号 (8字节)
        buffer.putLong(code);
        
        // 目标名称 (20字节)
        writeFixedString(buffer, name, 20);
        
        // 经度 (4字节) - 乘以10000000
        buffer.putInt((int) (longitude * 10000000));
        
        // 纬度 (4字节)
        buffer.putInt((int) (latitude * 10000000));
        
        // 横摇 (4字节) - 乘以10
        buffer.putInt((int) (random.nextGaussian() * 2 * 10));
        
        // 纵摇 (4字节)
        buffer.putInt((int) (random.nextGaussian() * 3 * 10));
        
        // 艏相角 (4字节)
        buffer.putInt((int) (random.nextDouble() * 360 * 10));
        
        // 速度方向 (4字节)
        buffer.putInt((int) (random.nextDouble() * 360 * 10));
        
        // 速度大小 (4字节)
        buffer.putInt((int) (15.0 * 10 + random.nextGaussian() * 2));
        
        // 时间 (8字节)
        buffer.putLong(System.currentTimeMillis());
        
        // 舵角 (4字节)
        buffer.putInt((int) (random.nextGaussian() * 10 * 1000 / 30));
        
        // 控制权 (1字节)
        buffer.put((byte) 0x01);
        
        // 控制模式 (1字节)
        buffer.put((byte) 0x02);
        
        // 惯导模式 (4字节)
        buffer.putInt(1);
        
        // 链路连接状态 (1字节)
        buffer.put((byte) 0x03);
    }

    /**
     * 写入固定长度字符串
     */
    private void writeFixedString(ByteBuffer buffer, String str, int length) {
        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        if (bytes.length >= length) {
            buffer.put(bytes, 0, length);
        } else {
            buffer.put(bytes);
            buffer.put(new byte[length - bytes.length]);
        }
    }

    /**
     * 完成数据包构建
     */
    private byte[] finalizePacket(ByteBuffer buffer) {
        int totalLength = buffer.position();
        
        // 更新包长度字段（位置7-8）
        buffer.putShort(7, (short) (totalLength - 48));
        
        byte[] data = new byte[totalLength];
        buffer.flip();
        buffer.get(data);
        return data;
    }

    /**
     * 简单的数据包分析
     */
    public void analyzePacket(byte[] data, String type) {
        System.out.println("--- " + type + "数据包分析 ---");
        ByteBuffer buffer = ByteBuffer.wrap(data);
        
        // 跳过报文头
        buffer.position(48);
        
        // 接口类型
        byte interfaceType = buffer.get();
        System.out.println("接口类型: 0x" + Integer.toHexString(interfaceType & 0xFF));
        
        // 目标数量
        byte targetCount = buffer.get();
        System.out.println("目标数量: " + (targetCount & 0xFF));
        
        // 分析每个目标的基本信息
        for (int i = 0; i < (targetCount & 0xFF); i++) {
            System.out.println("目标 " + (i + 1) + ":");
            
            byte seq = buffer.get();
            short targetType = buffer.getShort();
            long code = buffer.getLong();
            
            byte[] nameBytes = new byte[20];
            buffer.get(nameBytes);
            String name = new String(nameBytes, StandardCharsets.UTF_8).trim();
            
            System.out.println("  序号: " + seq + ", 类型: 0x" + Integer.toHexString(targetType & 0xFFFF) + 
                             ", 代号: " + code + ", 名称: '" + name + "'");
            
            // 跳过其余字段（根据类型不同，字段数量不同）
            if (targetType == 0x04) { // 无人机
                buffer.position(buffer.position() + 37); // 跳过无人机特有字段
            } else if (targetType == 0x03) { // 无人艇
                buffer.position(buffer.position() + 49); // 跳过无人艇特有字段
            }
        }
    }

    /**
     * 发送测试数据
     */
    public void sendTestData() throws IOException {
        System.out.println("发送到: " + TARGET_HOST + ":" + TARGET_PORT);
        
        for (int i = 0; i < 5; i++) {
            // 发送无人机数据
            byte[] uavData = generateUavPacket();
            sendUdpPacket(uavData);
            System.out.println("发送无人机数据包 " + (i + 1));
            
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            
            // 发送无人艇数据
            byte[] shipData = generateShipPacket();
            sendUdpPacket(shipData);
            System.out.println("发送无人艇数据包 " + (i + 1));
            
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 发送UDP数据包
     */
    private void sendUdpPacket(byte[] data) throws IOException {
        try (DatagramSocket socket = new DatagramSocket()) {
            InetAddress address = InetAddress.getByName(TARGET_HOST);
            DatagramPacket packet = new DatagramPacket(data, data.length, address, TARGET_PORT);
            socket.send(packet);
        }
    }
}
